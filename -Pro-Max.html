<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的专属浏览数据报告 (Pro Max+)</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts-wordcloud@2.1.0/dist/echarts-wordcloud.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        :root {
            --bg-color: #121212;
            --card-bg-color: rgba(30, 30, 30, 0.7);
            --text-color: #EAEAEA;
            --primary-color: #0A84FF;
            --secondary-color: #5E5CE6;
            --highlight-color: #BF5AF2;
            --green-color: #30D158;
            --font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --card-border-radius: 16px;
            --card-box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
            --card-backdrop-filter: blur(10px);
            --card-border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: var(--font-family);
            margin: 0;
            padding: 2rem;
            background-image: radial-gradient(circle at 1% 1%, rgba(10, 132, 255, 0.2) 0%, transparent 50%),
                              radial-gradient(circle at 99% 99%, rgba(191, 90, 242, 0.2) 0%, transparent 50%);
            background-attachment: fixed;
            min-height: 100vh;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 24px;
        }

        .header {
            grid-column: 1 / -1;
            text-align: center;
            padding-bottom: 2rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            background: -webkit-linear-gradient(45deg, var(--primary-color), var(--highlight-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header p {
            font-size: 1.1rem;
            color: rgba(235, 235, 245, 0.6);
            margin-top: 0.5rem;
        }

        .card {
            background: var(--card-bg-color);
            border-radius: var(--card-border-radius);
            box-shadow: var(--card-box-shadow);
            backdrop-filter: var(--card-backdrop-filter);
            -webkit-backdrop-filter: var(--card-backdrop-filter);
            border: var(--card-border);
            padding: 24px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.3);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 500;
            margin: 0 0 16px 0;
            color: var(--text-color);
            flex-shrink: 0;
        }

        .chart-container {
            flex-grow: 1;
            width: 100%;
            height: 100%;
            min-height: 0;
        }

        #overview-card { grid-column: 1 / 13; }
        #interest-radar-card { grid-column: 1 / 5; height: 450px; }
        #word-cloud-card { grid-column: 5 / 9; height: 450px; }
        #top-sites-card { grid-column: 9 / 13; height: 450px; }
        #heatmap-card { grid-column: 1 / 13; height: 320px; }

        .overview-stats {
            display: flex;
            justify-content: space-around;
            text-align: center;
            padding: 1rem 0;
        }

        .stat-item h3 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0;
            color: var(--primary-color);
        }
        .stat-item p {
            margin: 4px 0 0 0;
            color: rgba(235, 235, 245, 0.6);
        }
        
        #heatmap-card .chart-container {
             height: 250px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            #interest-radar-card { grid-column: 1 / 7; }
            #word-cloud-card { grid-column: 7 / 13; }
            #top-sites-card { grid-column: 1 / 13; }
        }

        @media (max-width: 768px) {
            body { padding: 1rem; }
            .container { grid-template-columns: 1fr; }
            .card { grid-column: 1 / -1 !important; height: auto; min-height: 400px; }
            #overview-card { min-height: auto; }
            #heatmap-card { min-height: 320px; }
        }

    </style>
</head>
<body>

    <div class="container">
        <header class="header">
            <h1>浏览数据洞察报告</h1>
            <p>基于您近两个月的浏览历史，由您的专属前端工程师为您倾力打造</p>
        </header>

        <div class="card" id="overview-card">
            <h2 class="card-title">智能总览</h2>
            <div class="overview-stats">
                <div class="stat-item">
                    <h3 id="total-visits">--</h3>
                    <p>总访问次数</p>
                </div>
                <div class="stat-item">
                    <h3 id="total-sites">--</h3>
                    <p>访问网站总数</p>
                </div>
                <div class="stat-item">
                    <h3 id="most-visited-site">--</h3>
                    <p>最常访问的网站</p>
                </div>
                <div class="stat-item">
                    <h3 id="busiest-day">--</h3>
                    <p>最活跃的一天</p>
                </div>
            </div>
        </div>

        <div class="card" id="interest-radar-card">
            <h2 class="card-title">兴趣罗盘</h2>
            <div class="chart-container" id="interest-radar"></div>
        </div>

        <div class="card" id="word-cloud-card">
            <h2 class="card-title">浏览全景</h2>
            <div class="chart-container" id="word-cloud"></div>
        </div>

        <div class="card" id="top-sites-card">
            <h2 class="card-title">访问 Top 10</h2>
            <div class="chart-container" id="top-sites-bar"></div>
        </div>

        <div class="card" id="heatmap-card">
            <h2 class="card-title">访问热力图</h2>
            <div class="chart-container" id="visit-heatmap"></div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const rawData = {
        "items": [
    {
      "id": "104903",
      "url": "https://github.com/hangwin/mcp-chrome/issues?q=is%3Aissue%20state%3Aclosed&page=3",
      "title": "Repo Issues",
      "lastVisitTime": 1752403386337.15,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104902",
      "url": "https://github.com/hangwin/mcp-chrome/issues?q=is%3Aissue%20state%3Aclosed&page=2",
      "title": "Repo Issues",
      "lastVisitTime": 1752403378377.597,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104901",
      "url": "https://deepchat.dev/",
      "title": "Deep Chat",
      "lastVisitTime": 1752403371184.986,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104900",
      "url": "https://www.google.com/search?q=deepchat&sourceid=chrome&ie=UTF-8",
      "title": "deepchat - Google 搜索",
      "lastVisitTime": 1752403367963.507,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104899",
      "url": "https://github.com/hangwin/mcp-chrome/issues/97",
      "title": "请问使用示例中出现的对话工具是什么 · Issue #97 · hangwin/mcp-chrome",
      "lastVisitTime": 1752403363568.592,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104898",
      "url": "https://github.com/hangwin/mcp-chrome/issues?q=is%3Aissue%20state%3Aclosed",
      "title": "Issues · hangwin/mcp-chrome",
      "lastVisitTime": 1752403360966.173,
      "visitCount": 4,
      "typedCount": 0
    },
    {
      "id": "104897",
      "url": "https://github.com/hangwin/mcp-chrome/issues/93",
      "title": "动态的数据怎样获取 · Issue #93 · hangwin/mcp-chrome",
      "lastVisitTime": 1752403355202.359,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104896",
      "url": "https://github.com/hangwin/mcp-chrome/issues",
      "title": "Issues · hangwin/mcp-chrome",
      "lastVisitTime": 1752403353168.852,
      "visitCount": 4,
      "typedCount": 0
    },
    {
      "id": "104890",
      "url": "https://github.com/hangwin/mcp-chrome/blob/master/README_zh.md",
      "title": "mcp-chrome/README_zh.md at master · hangwin/mcp-chrome",
      "lastVisitTime": 1752403344552.367,
      "visitCount": 6,
      "typedCount": 0
    },
    {
      "id": "104895",
      "url": "https://github.com/hangwin/mcp-chrome/tree/master/prompt",
      "title": "mcp-chrome/prompt at master · hangwin/mcp-chrome",
      "lastVisitTime": 1752403339913.69,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104894",
      "url": "https://www.youtube.com/watch?v=jf2UZfrR2Vk",
      "title": "(123) 使用chrome-mcp-server分析你的浏览记录 - YouTube",
      "lastVisitTime": 1752403327451.966,
      "visitCount": 4,
      "typedCount": 0
    },
    {
      "id": "104893",
      "url": "https://www.youtube.com/watch?v=jf2UZfrR2Vk&feature=youtu.be",
      "title": "使用chrome-mcp-server分析你的浏览记录 - YouTube",
      "lastVisitTime": 1752403325877.026,
      "visitCount": 3,
      "typedCount": 0
    },
    {
      "id": "360",
      "url": "https://www.youtube.com/",
      "title": "(123) YouTube",
      "lastVisitTime": 1752403227582.473,
      "visitCount": 12,
      "typedCount": 0
    },
    {
      "id": "104891",
      "url": "https://github.com/hangwin/mcp-chrome/blob/master/docs/TOOLS_zh.md",
      "title": "mcp-chrome/docs/TOOLS_zh.md at master · hangwin/mcp-chrome",
      "lastVisitTime": 1752403181525.943,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104889",
      "url": "https://github.com/hangwin/mcp-chrome?tab=readme-ov-file",
      "title": "hangwin/mcp-chrome: Chrome MCP Server is a Chrome extension-based Model Context Protocol (MCP) server that exposes your Chrome browser functionality to AI assistants like Claude, enabling complex browser automation, content analysis, and semantic search.",
      "lastVisitTime": 1752403081713.061,
      "visitCount": 3,
      "typedCount": 0
    },
    {
      "id": "104888",
      "url": "https://github.com/hangwin/mcp-chrome/releases",
      "title": "发布 · hangwin/mcp-chrome --- Releases · hangwin/mcp-chrome",
      "lastVisitTime": 1752402991815.541,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104887",
      "url": "https://github.com/hangwin/mcp-chrome/blob/master/prompt/excalidraw-prompt.md",
      "title": "mcp-chrome/prompt/excalidraw-prompt.md at master · hangwin/mcp-chrome",
      "lastVisitTime": 1752402975884.663,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104886",
      "url": "https://aijishu.com/link?target=https%3A%2F%2Fgithub.com%2Fshengqiangzhang%2Fexamples-of-web-crawlers%2Ftree%2Fmaster%2F11.%25E4%25B8%2580%25E9%2594%25AE%25E5%2588%2586%25E6%259E%2590%25E4%25BD%25A0%25E7%259A%2584%25E4%25B8%258A%25E7%25BD%2591%25E8%25A1%258C%25E4%25B8%25BA%28web%25E9%25A1%25B5%25E9%259D%25A2%25E5%258F%25AF%25E8%25A7%2586%25E5%258C%2596",
      "title": "极术社区 - 连接开发者与智能计算生态",
      "lastVisitTime": 1752402959235.053,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104885",
      "url": "https://aijishu.com/a/1060000000008148",
      "title": "一键分析你的上网行为, 看看你平时上网都在干嘛? - 极术社区 - 连接开发者与智能计算生态",
      "lastVisitTime": 1752402952449.159,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104884",
      "url": "https://chromewebstore.google.com/detail/%E7%BD%91%E8%B4%B9%E5%BE%88%E8%B4%B5-%E4%B8%8A%E7%BD%91%E6%97%B6%E9%97%B4%E7%BB%9F%E8%AE%A1/dkdhhcbjijekmneelocdllcldcpmekmm?hl=zh-CN",
      "title": "网费很贵 - 上网时间统计 - Chrome 应用商店",
      "lastVisitTime": 1752402945492.662,
      "visitCount": 3,
      "typedCount": 0
    },
    {
      "id": "104883",
      "url": "https://www.google.com/search?q=chrome+%E6%B5%8F%E8%A7%88%E8%AE%B0%E5%BD%95+%E5%88%86%E6%9E%90+github&sca_esv=440a43a3eb6979b0&sxsrf=AE3TifPjD8F04PpgMtkYgBDDN4SD6grEbA:1752402893911&ei=zYtzaJCyN72_kPIP2661yQk&start=10&sa=N&sstk=Ac65TH68MDqQ0WJE2dyrn9RLKmj6ZPojWENN4LN_6OOsFGK4CX9_Z_Ui8UXNRibIKAzSxXvVeqqGDJhY1TpJqDBcGdl0CqJC1cx8lw&ved=2ahUKEwjQzPq_0bmOAxW9H0QIHVtXLZkQ8tMDegQIChAE&biw=1460&bih=804&dpr=1",
      "title": "chrome 浏览记录 分析 github - Google 搜索",
      "lastVisitTime": 1752402915858.133,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104882",
      "url": "https://github.com/tuian/chrome-history-stat",
      "title": "tuian/chrome-history-stat: 分析你的 Chrome/Firefox 浏览记录",
      "lastVisitTime": 1752402898222.955,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104881",
      "url": "https://github.com/yeyuguo/chrome-history-stat",
      "title": "yeyuguo/chrome-history-stat: 分析你的 Chrome 浏览记录",
      "lastVisitTime": 1752402896495.464,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104880",
      "url": "https://www.google.com/search?q=chrome+%E6%B5%8F%E8%A7%88%E8%AE%B0%E5%BD%95+%E5%88%86%E6%9E%90+github&oq=chrome+%E6%B5%8F%E8%A7%88%E8%AE%B0%E5%BD%95+%E5%88%86%E6%9E%90+github&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIKCAEQABiiBBiJBTIKCAIQABiABBiiBDIKCAMQABiABBiiBDIKCAQQABiABBiiBNIBCDk1MTVqMGo0qAIAsAIB&sourceid=chrome&ie=UTF-8",
      "title": "chrome 浏览记录 分析 github - Google 搜索",
      "lastVisitTime": 1752402893873.4,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104879",
      "url": "https://github.com/hangwin/mcp-chrome",
      "title": "hangwin/mcp-chrome: Chrome MCP Server is a Chrome extension-based Model Context Protocol (MCP) server that exposes your Chrome browser functionality to AI assistants like Claude, enabling complex browser automation, content analysis, and semantic search.",
      "lastVisitTime": 1752402881433.688,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104870",
      "url": "https://linux.do/t/topic/714231",
      "title": "【硬核开源mcp-chrome】一个chrome插件，能让任意chatbot接管你的chrome浏览器 - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402877710.083,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "104878",
      "url": "https://linux.do/t/topic/714231/1",
      "title": "【硬核开源mcp-chrome】一个chrome插件，能让任意chatbot接管你的chrome浏览器 - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402876681.963,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104877",
      "url": "https://linux.do/t/topic/714231/232",
      "title": "【硬核开源mcp-chrome】一个chrome插件，能让任意chatbot接管你的chrome浏览器 - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402875875.773,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104876",
      "url": "https://linux.do/t/topic/714231/236",
      "title": "【硬核开源mcp-chrome】一个chrome插件，能让任意chatbot接管你的chrome浏览器 - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402873044.879,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104875",
      "url": "https://linux.do/t/topic/697447/22",
      "title": "分享开发中好用的 ai 提示词或者 MCP - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402798043.99,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104874",
      "url": "https://linux.do/t/topic/697447/21",
      "title": "分享开发中好用的 ai 提示词或者 MCP - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402789693.915,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104873",
      "url": "https://linux.do/t/topic/697447/14",
      "title": "分享开发中好用的 ai 提示词或者 MCP - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402788270.89,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104872",
      "url": "https://linux.do/t/topic/697447/11",
      "title": "分享开发中好用的 ai 提示词或者 MCP - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402786976.724,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104871",
      "url": "https://linux.do/search?q=mcp",
      "title": "'mcp' 的搜索结果 - LINUX DO",
      "lastVisitTime": 1752402778422.437,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104869",
      "url": "https://linux.do/t/topic/697447",
      "title": "分享开发中好用的 ai 提示词或者 MCP - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402770429.176,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "77693",
      "url": "https://linux.do/",
      "title": "LINUX DO - 新的理想型社区",
      "lastVisitTime": 1752402758187.717,
      "visitCount": 410,
      "typedCount": 0
    },
    {
      "id": "104868",
      "url": "https://github.com/pocketbase/pocketbase",
      "title": "pocketbase/pocketbase：1 个文件中的开源实时后端 --- pocketbase/pocketbase: Open Source realtime backend in 1 file",
      "lastVisitTime": 1752402723268.255,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104867",
      "url": "https://github.com/GyulyVGC/sniffnet?tab=readme-ov-file",
      "title": "GyulyVGC/sniffnet：轻松监控您的互联网流量🕵️‍♂️ --- GyulyVGC/sniffnet: Comfortably monitor your Internet traffic 🕵️‍♂️",
      "lastVisitTime": 1752402705335.498,
      "visitCount": 3,
      "typedCount": 0
    },
    {
      "id": "104866",
      "url": "https://github.com/GyulyVGC/sniffnet",
      "title": "GyulyVGC/sniffnet: Comfortably monitor your Internet traffic 🕵️‍♂️",
      "lastVisitTime": 1752402678193.303,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104865",
      "url": "https://github.com/trending?since=weekly",
      "title": "Trending repositories on GitHub this week",
      "lastVisitTime": 1752402667462.16,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104864",
      "url": "https://github.com/snap-stanford/Biomni",
      "title": "snap-stanford/Biomni：Biomni：通用生物医学 AI 代理 --- snap-stanford/Biomni: Biomni: a general-purpose biomedical AI agent",
      "lastVisitTime": 1752402656512.636,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104863",
      "url": "https://github.com/gorhill/uBlock",
      "title": "gorhill/uBlock: uBlock Origin - An efficient blocker for Chromium and Firefox. Fast and lean.",
      "lastVisitTime": 1752402613776.433,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104862",
      "url": "https://github.com/landing-ai/agentic-doc",
      "title": "landing-ai/agentic-doc：用于从 LandingAI 提取 Agentic 文档的 Python 库 --- landing-ai/agentic-doc: Python library for Agentic Document Extraction from LandingAI",
      "lastVisitTime": 1752402611250.224,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104861",
      "url": "https://zh.wikipedia.org/wiki/%E6%BC%A2%E8%AA%9E%E9%9B%99%E6%8B%BC%E7%9B%B2%E6%96%87",
      "title": "汉语双拼盲文 - 维基百科，自由的百科全书",
      "lastVisitTime": 1752402494815.512,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104860",
      "url": "https://zh.wikipedia.org/wiki/%E7%9B%B2%E6%96%87",
      "title": "盲文 - 维基百科，自由的百科全书",
      "lastVisitTime": 1752402393583.941,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104858",
      "url": "https://www.google.com/search?q=%E7%9B%B2%E6%96%87&oq=%E7%9B%B2%E6%96%87&gs_lcrp=EgZjaHJvbWUyCQgAEEUYORiABDIHCAEQABiABDIHCAIQABiABDIHCAMQABiABDIHCAQQABiABDIHCAUQABiABDIHCAYQABiABDIHCAcQABiABDIWCAgQLhivARjHARiABBiYBRiZBRieBTIHCAkQABiABNIBCDIwNDJqMGo3qAIAsAIA&sourceid=chrome&ie=UTF-8",
      "title": "盲文 - Google 搜索",
      "lastVisitTime": 1752402388903.224,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104654",
      "url": "https://github.com/googleapis/genai-toolbox",
      "title": "googleapis/genai-toolbox：数据库的 MCP 工具箱是一个用于数据库的开源 MCP 服务器。 --- googleapis/genai-toolbox: MCP Toolbox for Databases is an open source MCP server for databases.",
      "lastVisitTime": 1752402342281.843,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "59",
      "url": "https://github.com/trending",
      "title": "Trending repositories on GitHub today",
      "lastVisitTime": 1752402321901.743,
      "visitCount": 34,
      "typedCount": 0
    },
    {
      "id": "1012",
      "url": "https://www.bilibili.com/v/popular/all",
      "title": "哔哩哔哩热门",
      "lastVisitTime": 1752402318483.013,
      "visitCount": 204,
      "typedCount": 0
    },
    {
      "id": "1011",
      "url": "https://www.bilibili.com/v/popular/all?spm_id_from=..0.0",
      "title": "哔哩哔哩热门",
      "lastVisitTime": 1752402317273.839,
      "visitCount": 198,
      "typedCount": 0
    },
    {
      "id": "104857",
      "url": "https://linux.do/t/topic/264113",
      "title": "向僵尸开炮服务端，增加全物品后台 - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402310002.594,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104856",
      "url": "https://linux.do/t/topic/783519",
      "title": "美团 0元吃冰 补了蜜雪大圣代3选1 - 福利羊毛 - LINUX DO",
      "lastVisitTime": 1752402289610.13,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104855",
      "url": "https://linux.do/t/topic/783217/9",
      "title": "用kimi整理桌面血压上来了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402222063.881,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104854",
      "url": "https://linux.do/t/topic/783217/7",
      "title": "用kimi整理桌面血压上来了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402220301.112,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104853",
      "url": "https://linux.do/t/topic/783217/3",
      "title": "用kimi整理桌面血压上来了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402215668.455,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104852",
      "url": "https://linux.do/t/topic/783217",
      "title": "用kimi整理桌面血压上来了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402186032.771,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104851",
      "url": "https://linux.do/t/topic/779171/18",
      "title": "【RSS + 播客】基于 L 站热帖生成播客 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402175246.796,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104850",
      "url": "https://linux.do/t/topic/779171",
      "title": "【RSS + 播客】基于 L 站热帖生成播客 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402156428.439,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104849",
      "url": "https://linux.do/t/topic/781756",
      "title": "【开源】智能，安全的 KEY 轮询网关 (减少被 ban & 429 的概率) - 资源荟萃 - LINUX DO",
      "lastVisitTime": 1752402150035.23,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104848",
      "url": "https://linux.do/t/topic/783391/6",
      "title": "有人看脱口秀综艺吗，我感觉被夹带私活了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402132707.345,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104847",
      "url": "https://linux.do/t/topic/783391/2",
      "title": "有人看脱口秀综艺吗，我感觉被夹带私活了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402128958.085,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104846",
      "url": "https://linux.do/t/topic/783490/11",
      "title": "兄弟们，夸克网盘SVIP会员限速，怎么破啊 - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402113083.291,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104845",
      "url": "https://linux.do/t/topic/783490/7",
      "title": "兄弟们，夸克网盘SVIP会员限速，怎么破啊 - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402112293.792,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104844",
      "url": "https://linux.do/t/topic/783490/5",
      "title": "兄弟们，夸克网盘SVIP会员限速，怎么破啊 - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402109986.664,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104843",
      "url": "https://linux.do/t/topic/783490",
      "title": "兄弟们，夸克网盘SVIP会员限速，怎么破啊 - 开发调优 - LINUX DO",
      "lastVisitTime": 1752402101694.355,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104842",
      "url": "https://linux.do/t/topic/783391",
      "title": "有人看脱口秀综艺吗，我感觉被夹带私活了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402096325.659,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104841",
      "url": "https://linux.do/t/topic/783526/7",
      "title": "Gemini 懂得如何甩锅了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402090918.829,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104840",
      "url": "https://linux.do/t/topic/783526",
      "title": "Gemini 懂得如何甩锅了 - 搞七捻三 - LINUX DO",
      "lastVisitTime": 1752402059781.492,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104839",
      "url": "https://www.bilibili.com/video/BV1s6M9zPEBS/",
      "title": "沧海桑田，到底什么时候才能打赢第五局？_英雄联盟",
      "lastVisitTime": 1752401516123.889,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104837",
      "url": "https://www.bilibili.com/video/BV1GuFzmEZM/",
      "title": "【漫士】只会聊天的AI，怎么才能变成真正的助手？_哔哩哔哩_bilibili",
      "lastVisitTime": 1752401495922.569,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104836",
      "url": "https://www.bilibili.com/video/BV1KF3bzKEk5/",
      "title": "专业挑战室 | 《绝区零》战斗设计幕后 第一期_游戏热门视频",
      "lastVisitTime": 1752401242935.947,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "1016",
      "url": "https://t.bilibili.com/",
      "title": "动态首页-哔哩哔哩",
      "lastVisitTime": 1752401236086.289,
      "visitCount": 293,
      "typedCount": 0
    },
    {
      "id": "1015",
      "url": "https://t.bilibili.com/?spm_id_from=..0.0",
      "title": "动态首页-哔哩哔哩",
      "lastVisitTime": 1752401234626.743,
      "visitCount": 188,
      "typedCount": 0
    },
    {
      "id": "104835",
      "url": "https://www.bilibili.com/video/BV1otuFziEhU/",
      "title": "我见了小岛秀夫一面，并给了他没订的货物_游戏热门视频",
      "lastVisitTime": 1752400628995.003,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104834",
      "url": "https://www.bilibili.com/video/BV1otuFziEhU/?spm_id_from=..0.0",
      "title": "我见了小岛秀夫一面，并给了他没订的货物_游戏热门视频",
      "lastVisitTime": 1752400628110.923,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "104832",
      "url": "https://nga.178.com/read.php?tid=44597721",
      "title": "bug氵 疑似界园肉鸽预热？ 178 P1",
      "lastVisitTime": 1752400610676.943,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "14",
      "url": "https://nga.178.com/thread.php?fid=-34587507&rand=659",
      "title": "明日方舟-罗德岛驻艾泽拉斯大使馆 178 P1",
      "lastVisitTime": 1752400610586.01,
      "visitCount": 511,
      "typedCount": 0
    },
    {
      "id": "104831",
      "url": "https://nga.178.com/read.php?tid=44597765",
      "title": "整个界园肉鸽给我感觉就是我被岁家做局了 178 P1",
      "lastVisitTime": 1752400605938.667,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "104830",
      "url": "https://nga.178.com/read.php?tid=44596491",
      "title": "这是肉鸽宣传PV吗？确定不是小姨带货PV吗？ 178 P1",
      "lastVisitTime": 1752400560757.815,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "104829",
      "url": "https://nga.178.com/read.php?tid=44598535",
      "title": "raidian的模组可能是代替肉鸽天赋存在的 178 P1",
      "lastVisitTime": 1752400545936.943,
      "visitCount": 2,
      "typedCount": 0
    },
    {
      "id": "104828",
      "url": "https://nga.178.com/read.php?tid=44597735",
      "title": "还是希望raidian可以不领任务来自行控制练度 178",
      "lastVisitTime": 1752400526422.043,
      "visitCount": 1,
      "typedCount": 0
    },
    {
      "id": "2",
      "url": "https://www.bilibili.com/",
      "title": "哔哩哔哩 (゜-゜)つロ 干杯~-bilibili",
      "lastVisitTime": 1752400340227.074,
      "visitCount": 352,
      "typedCount": 0
    },
    {
      "id": "5",
      "url": "https://alterhomepage.alteration.top/",
      "title": "Alter Homepage",
      "lastVisitTime": 1752400338476.876,
      "visitCount": 223,
      "typedCount": 0
    },
    {
      "id": "3",
      "url": "https://www.zhihu.com/",
      "title": "(24 封私信 / 10 条消息) 首页 - 知乎",
      "lastVisitTime": 1752399055160.582,
      "visitCount": 534,
      "typedCount": 0
    },
    {
      "id": "1361",
      "url": "https://www.zhihu.com/hot",
      "title": "(24 封私信 / 10 条消息) 首页 - 知乎",
      "lastVisitTime": 1752399053375.343,
      "visitCount": 281,
      "typedCount": 0
    }
    ]};
    const historyItems = rawData.items;

    // --- Data Processing Functions ---
    function getHostname(url) {
        try {
            return new URL(url).hostname.replace(/^www\./, '');
        } catch (e) {
            // For URLs like "chrome-extension://...", return a special name
            if (url && url.startsWith('chrome-extension://')) {
                return 'Chrome Extension';
            }
            return 'invalid_url';
        }
    }

    // --- Initialize Charts ---
    const topSitesChart = echarts.init(document.getElementById('top-sites-bar'));
    const interestRadarChart = echarts.init(document.getElementById('interest-radar'));
    const wordCloudChart = echarts.init(document.getElementById('word-cloud'));
    const heatmapChart = echarts.init(document.getElementById('visit-heatmap'));


    // --- 1. Process Domain Data & Top Sites Chart ---
    const siteVisitCounts = {};
    let totalVisits = 0;

    historyItems.forEach(item => {
        const hostname = getHostname(item.url);
        if (hostname !== 'invalid_url') {
            siteVisitCounts[hostname] = (siteVisitCounts[hostname] || 0) + item.visitCount;
            totalVisits += item.visitCount;
        }
    });

    const sortedSites = Object.entries(siteVisitCounts).sort(([, a], [, b]) => b - a);
    
    const topSites = sortedSites.slice(0, 10).reverse();
    const topSitesLabels = topSites.map(item => item[0]);
    const topSitesData = topSites.map(item => item[1]);

    topSitesChart.setOption({
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: { type: 'value', boundaryGap: [0, 0.01], splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } }, axisLabel: { color: '#EAEAEA' } },
        yAxis: { type: 'category', data: topSitesLabels, axisLabel: { color: '#EAEAEA' } },
        series: [{
            type: 'bar',
            data: topSitesData,
            itemStyle: {
                borderRadius: [0, 5, 5, 0],
                color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    { offset: 0, color: '#0A84FF' },
                    { offset: 1, color: '#5E5CE6' }
                ])
            },
            label: { show: true, position: 'right', color: '#EAEAEA' }
        }],
        textStyle: { fontFamily: 'Noto Sans SC, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif' }
    });

    // --- 2. Process & Render Interest Radar Chart ---
    const categories = {
        '开发&技术': { keywords: ['github.com', 'linux.do', 'v2ex.com', 'portainer.alteration.top', 'deno.com', 'stackoverflow.com', 'localhost'], value: 0 },
        'AI&机器学习': { keywords: ['aistudio.google.com', 'openai.com', 'huggingface.co', 'lobehub.com', 'anthropic.com', 'moonshot.cn', 'siliconflow.cn', 'mcp.so', 'aihubmix.com', 'cerebras.ai', 'deepchat.dev'], value: 0 },
        '游戏&娱乐': { keywords: ['bilibili.com', 'nga.178.com', 'prts.wiki', 'youtube.com', 'twitch.tv', 'op.gg', 'yuanshen.com', 'mihoyo.com', 'biligame.com'], value: 0 },
        '金融&股票': { keywords: ['tushare.pro', 'finnhub.io', 'eastmoney.com', '10jqka.com.cn', 'sina.com.cn'], value: 0 },
        '学习&知识': { keywords: ['zhihu.com', 'wikipedia.org', 'fenbi.com', 'promptingguide.ai', 'scientificamerican.com', 'ted.com'], value: 0 },
        '其他': { keywords: ['google.com', 'Chrome Extension'], value: 0 }
    };

    sortedSites.forEach(([hostname, count]) => {
        let assigned = false;
        for (const category in categories) {
            if (categories[category].keywords.some(kw => hostname.includes(kw))) {
                categories[category].value += count;
                assigned = true;
                break;
            }
        }
        if (!assigned) {
             categories['其他'].value += count;
        }
    });
    
    const radarValues = Object.values(categories).map(c => c.value);
    const maxRadarValue = Math.max(...radarValues);
    const radarIndicator = Object.keys(categories).map(name => ({ name: name, max: maxRadarValue * 1.1 })); // Add a small buffer

    interestRadarChart.setOption({
        tooltip: { trigger: 'item' },
        radar: {
            indicator: radarIndicator,
            center: ['50%', '55%'],
            radius: '75%',
            axisName: { color: '#EAEAEA', fontSize: 14 },
            splitArea: { areaStyle: { color: ['rgba(94, 92, 230, 0.1)', 'rgba(10, 132, 255, 0.1)'], shadowColor: 'rgba(0, 0, 0, 0.2)', shadowBlur: 10 } },
            splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.2)' } },
            axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.2)' } }
        },
        series: [{
            type: 'radar',
            data: [{
                value: radarValues,
                name: '兴趣分布',
                areaStyle: { color: 'rgba(191, 90, 242, 0.6)' },
                lineStyle: { color: '#BF5AF2' },
                itemStyle: { color: '#BF5AF2' }
            }]
        }],
        textStyle: { fontFamily: 'Noto Sans SC, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif' }
    });

    // --- 3. Process & Render Word Cloud Chart ---
    const titleCounts = {};
    const commonSuffixes = ['- Google 搜索', '- 知乎', '· LobeChat', ' - 哔哩哔哩', '_bilibili', '- LINUX DO', '- PRTS - 玩家共同构筑的明日方舟中文Wiki', '...'];
    
    historyItems.forEach(item => {
        let title = item.title.trim();
        if (!title) return;

        commonSuffixes.forEach(suffix => {
            if (title.endsWith(suffix)) {
                title = title.substring(0, title.length - suffix.length).trim();
            }
        });
        
        // Improved word tokenization
        const words = title.match(/[\u4e00-\u9fa5]+|[a-zA-Z0-9]+/g) || [];

        words.forEach(word => {
            if (word && word.length > 1 && !/^\d+$/.test(word)) {
                titleCounts[word] = (titleCounts[word] || 0) + item.visitCount; // Use visitCount for weight
            }
        });
    });

    const wordCloudData = Object.entries(titleCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 120)
        .map(([name, value]) => ({ name, value }));

    wordCloudChart.setOption({
        tooltip: { show: true },
        series: [{
            type: 'wordCloud',
            sizeRange: [12, 55],
            rotationRange: [-45, 45],
            rotationStep: 45,
            gridSize: 8,
            shape: 'diamond',
            textStyle: {
                fontFamily: 'Noto Sans SC, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif',
                color: function () {
                    const colors = ['#0A84FF', '#5E5CE6', '#BF5AF2', '#30D158', '#FF9F0A', '#FF453A', '#EAEAEA'];
                    return colors[Math.floor(Math.random() * colors.length)];
                }
            },
            data: wordCloudData
        }]
    });


    // --- 4. Process & Render Heatmap Chart ---
    const heatmapData = {};
    let visitTimestamps = [];

    historyItems.forEach(item => {
        const visitDate = new Date(item.lastVisitTime);
        if (!isNaN(visitDate.getTime())) {
            const dateString = echarts.format.formatTime('yyyy-MM-dd', visitDate);
            heatmapData[dateString] = (heatmapData[dateString] || 0) + item.visitCount;
            visitTimestamps.push(visitDate);
        }
    });

    const heatmapSeriesData = Object.entries(heatmapData).map(([date, count]) => [date, count]);
    const busiestDayEntry = Object.entries(heatmapData).sort(([, a], [, b]) => b - a)[0];

    visitTimestamps.sort((a, b) => a - b);
    const startDate = visitTimestamps.length > 0 ? visitTimestamps[0] : new Date();
    const endDate = visitTimestamps.length > 0 ? visitTimestamps[visitTimestamps.length - 1] : new Date();

    heatmapChart.setOption({
        tooltip: {
            position: 'top',
            formatter: p => `${echarts.format.formatTime('yyyy-MM-dd', p.data[0])}: ${p.data[1]} 次访问`
        },
        visualMap: {
            min: 0,
            max: Math.max(...Object.values(heatmapData)),
            calculable: true,
            orient: 'horizontal',
            left: 'center',
            bottom: '0%',
            inRange: { color: ['#2C2C2E', '#0A84FF', '#BF5AF2'] },
            textStyle: { color: 'var(--text-color)' }
        },
        calendar: {
            top: 50, left: 30, right: 30,
            cellSize: ['auto', 20],
            range: [echarts.format.formatTime('yyyy-MM-dd', startDate), echarts.format.formatTime('yyyy-MM-dd', endDate)],
            itemStyle: { borderWidth: 4, borderColor: 'var(--bg-color)' },
            dayLabel: { nameMap: 'zh', color: 'var(--text-color)' },
            monthLabel: { nameMap: 'zh', color: 'var(--text-color)' },
            yearLabel: { show: true, color: 'var(--text-color)' }
        },
        series: { type: 'heatmap', coordinateSystem: 'calendar', data: heatmapSeriesData },
        textStyle: { fontFamily: 'var(--font-family)' }
    });
    
    // --- 5. Update Overview Stats ---
    document.getElementById('total-visits').textContent = totalVisits.toLocaleString();
    document.getElementById('total-sites').textContent = sortedSites.length.toLocaleString();
    if (sortedSites.length > 0) {
        document.getElementById('most-visited-site').textContent = sortedSites[0][0];
    } else {
        document.getElementById('most-visited-site').textContent = '暂无数据';
    }
    if (busiestDayEntry && busiestDayEntry[0]) {
        const busiestDate = new Date(busiestDayEntry[0]);
        if (!isNaN(busiestDate.getTime())) {
            document.getElementById('busiest-day').textContent = busiestDate.toLocaleDateString('zh-CN', {month: 'long', day: 'numeric'});
        } else {
            document.getElementById('busiest-day').textContent = '暂无数据';
        }
    } else {
        document.getElementById('busiest-day').textContent = '暂无数据';
    }


    // --- Final Step: Resize charts on window resize ---
    window.addEventListener('resize', function() {
        topSitesChart.resize();
        interestRadarChart.resize();
        wordCloudChart.resize();
        heatmapChart.resize();
    });
});
</script>

</body>
</html>
